import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, ANY, call
import copy
from datetime import datetime, timedelta, timezone
from freezegun import freeze_time
from fastapi import HTTPException

from packages.backend.impl.services.EncounterService import (
    EncounterServiceImpl,
    POST_DISCHARGE_PATIENT_SMS,
    POST_DISCHARGE_RELATIVE_SMS,
)
from packages.backend.models.encounter import Encounter, EncounterId, ShareId, Discharge
from packages.backend.models.patient import (
    Patient,
    PatientId,
    HumanName,
    Contact,
    Telecom,
)
from packages.backend.models.common import Period, Identifier, ISODate, UnixTimestamp
from packages.backend.models.organization import OrganizationId, HealthcareServiceId
from packages.backend.models.document_reference import Attachment
from packages.backend.spi.repositories import PutEncounterRequest, PatchEncounterRequest


# Fixtures
@pytest_asyncio.fixture
def mock_encounter_repo():
    return AsyncMock()


@pytest_asyncio.fixture
def mock_patient_repo():
    return AsyncMock()


@pytest_asyncio.fixture
def mock_organization_repo():
    return AsyncMock()


@pytest_asyncio.fixture
def mock_notification_service():
    return AsyncMock()


@pytest_asyncio.fixture
def mock_event_repository():
    return AsyncMock()


@pytest.fixture
def encounter_service(
    mock_encounter_repo: AsyncMock,
    mock_patient_repo: AsyncMock,
    mock_organization_repo: AsyncMock,
    mock_notification_service: AsyncMock,
    mock_event_repository: AsyncMock,
):
    return EncounterServiceImpl(
        encounter_repository=mock_encounter_repo,
        patient_repository=mock_patient_repo,
        organization_repository=mock_organization_repo,
        notification_service=mock_notification_service,
        event_repository=mock_event_repository,
    )


# Test Data
FROZEN_NOW = datetime(2024, 2, 25, 12, 0, 0, tzinfo=timezone.utc)
TWO_HOURS_AGO = FROZEN_NOW - timedelta(hours=2)
THREE_HOURS_AGO = FROZEN_NOW - timedelta(hours=3)
ONE_HOUR_AGO = FROZEN_NOW - timedelta(hours=1)

# Dummy IDs for required fields
PATIENT_ID_1 = PatientId("patient-1")
PATIENT_ID_NO_PHONE = PatientId("patient-no-phone")
PATIENT_ID_WITH_CONTACTS = PatientId("patient-with-contacts")
ENCOUNTER_ID_1 = EncounterId("enc-1")
ORG_ID = OrganizationId("org-1")
HS_ID = HealthcareServiceId("hs-1")
SHARE_ID_1 = ShareId("share-1")
IDENTIFIER_1 = Identifier(system="sys", value="val")

BASE_URL = "https://ppu.urgenceschrono.com"

# Patients
PATIENT_1_PHONE = Patient(
    patient_id=PATIENT_ID_1,
    name=HumanName(first_name="Test", last_name="PatientPhone"),
    birth_date=ISODate("1990-01-01"),
    phone="+33611111111",
    contact=[],
)

PATIENT_2_NO_PHONE = Patient(
    patient_id=PATIENT_ID_NO_PHONE,
    name=HumanName(first_name="Test", last_name="PatientNoPhone"),
    birth_date=ISODate("1991-02-02"),
    phone=None,
    contact=[],
)

PATIENT_3_WITH_CONTACTS = Patient(
    patient_id=PATIENT_ID_WITH_CONTACTS,
    name=HumanName(first_name="Test", last_name="PatientContacts"),
    birth_date=ISODate("1992-03-03"),
    phone="+33633333333",
    contact=[
        Contact(telecom=[Telecom(system="sms", value="+33711111111")]),
        Contact(telecom=[Telecom(system="sms", value="+33722222222")]),
    ],
)

PATIENT_4_NO_PHONE_WITH_CONTACTS = Patient(
    patient_id=PatientId("patient-4-no-phone-contacts"),
    name=HumanName(first_name="Test", last_name="NoPhoneContacts"),
    birth_date=ISODate("1993-04-04"),
    phone=None,
    contact=[Contact(telecom=[Telecom(system="sms", value="+33744444444")])],
)


# Encounters (mostly for delayed SMS tests)
ENCOUNTER_BASE = Encounter(
    encounter_id=ENCOUNTER_ID_1,
    share_id=SHARE_ID_1,
    subject=PATIENT_ID_1,
    service_provider=ORG_ID,
    service_type=HS_ID,
    identifier=[IDENTIFIER_1],
    actual_period=Period(start=UnixTimestamp(0), end=None),
    post_discharge_sms_sent_at=None,
)

ENCOUNTER_DISCHARGED_3H_AGO = copy.deepcopy(ENCOUNTER_BASE)
ENCOUNTER_DISCHARGED_3H_AGO.subject = PATIENT_ID_1
ENCOUNTER_DISCHARGED_3H_AGO.actual_period.end = UnixTimestamp(
    int(THREE_HOURS_AGO.timestamp() * 1000)
)

ENCOUNTER_DISCHARGED_1H_AGO = copy.deepcopy(ENCOUNTER_BASE)
ENCOUNTER_DISCHARGED_1H_AGO.encounter_id = EncounterId("enc-discharged-1h-ago")
ENCOUNTER_DISCHARGED_1H_AGO.share_id = ShareId("share-discharged-1h-ago")
ENCOUNTER_DISCHARGED_1H_AGO.subject = PATIENT_ID_1
ENCOUNTER_DISCHARGED_1H_AGO.actual_period.end = UnixTimestamp(
    int(ONE_HOUR_AGO.timestamp() * 1000)
)

ENCOUNTER_SMS_ALREADY_SENT = copy.deepcopy(ENCOUNTER_BASE)
ENCOUNTER_SMS_ALREADY_SENT.encounter_id = EncounterId("enc-sms-sent")
ENCOUNTER_SMS_ALREADY_SENT.share_id = ShareId("share-sms-sent")
ENCOUNTER_SMS_ALREADY_SENT.subject = PATIENT_ID_1
ENCOUNTER_SMS_ALREADY_SENT.actual_period.end = UnixTimestamp(
    int(THREE_HOURS_AGO.timestamp() * 1000)
)
ENCOUNTER_SMS_ALREADY_SENT.post_discharge_sms_sent_at = UnixTimestamp(
    int(TWO_HOURS_AGO.timestamp() * 1000)
)

ENCOUNTER_NOT_DISCHARGED = copy.deepcopy(ENCOUNTER_BASE)
ENCOUNTER_NOT_DISCHARGED.encounter_id = EncounterId("enc-not-discharged")
ENCOUNTER_NOT_DISCHARGED.share_id = ShareId("share-not-discharged")
ENCOUNTER_NOT_DISCHARGED.subject = PATIENT_ID_1

ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO = copy.deepcopy(ENCOUNTER_BASE)
ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO.encounter_id = EncounterId(
    "enc-no-phone-discharge"
)
ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO.share_id = ShareId("share-no-phone-discharge")
ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO.subject = PATIENT_ID_NO_PHONE
ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO.actual_period.end = UnixTimestamp(
    int(THREE_HOURS_AGO.timestamp() * 1000)
)


# Sample Attachments for Discharge Instructions
ATTACHMENT_1 = Attachment(id="instr-1", name="Instruction 1")
ATTACHMENT_2 = Attachment(id="instr-2", name="Instruction 2")
ATTACHMENT_3 = Attachment(id="instr-3", name="Instruction 3")

# Base Encounter for update tests (no discharge initially)
ENCOUNTER_FOR_UPDATE = copy.deepcopy(ENCOUNTER_BASE)
ENCOUNTER_FOR_UPDATE.encounter_id = EncounterId("enc-update-base")
ENCOUNTER_FOR_UPDATE.share_id = ShareId("share-update-base")
ENCOUNTER_FOR_UPDATE.subject = PATIENT_1_PHONE.patient_id
ENCOUNTER_FOR_UPDATE.discharge = None  # Start with no discharge info


# --- create_encounter Tests ---


@pytest.mark.asyncio
async def test_create_encounter_patient_with_phone_no_contacts(
    encounter_service,
    mock_encounter_repo,
    mock_patient_repo,
    mock_organization_repo,
    mock_notification_service,
):
    """SMS sent to patient only when patient has phone and no contacts."""
    patient = PATIENT_1_PHONE
    put_request = PutEncounterRequest(
        subject=patient.patient_id,
        service_provider=ORG_ID,
        service_type=HS_ID,
        identifier=[IDENTIFIER_1],
    )
    created_encounter = copy.deepcopy(ENCOUNTER_BASE)
    created_encounter.subject = patient.patient_id

    mock_patient_repo.get_patient_by_id.return_value = patient
    mock_organization_repo.get_organization_by_id.return_value = AsyncMock()
    mock_encounter_repo.create_encounter.return_value = created_encounter

    await encounter_service.create_encounter(put_request)

    mock_notification_service.send_sms.assert_called_once_with(
        phone=patient.phone,
        content=f"Pour suivre l'avancée de votre parcours aux urgences du CHU de Limoges, cliquez sur ce lien: {BASE_URL}/sign-in/{created_encounter.encounter_id}",
    )


@pytest.mark.asyncio
async def test_create_encounter_patient_no_phone_with_contacts(
    encounter_service,
    mock_encounter_repo,
    mock_patient_repo,
    mock_organization_repo,
    mock_notification_service,
):
    """SMS sent to relative only when patient has no phone but has SMS contacts."""
    patient = PATIENT_4_NO_PHONE_WITH_CONTACTS
    put_request = PutEncounterRequest(
        subject=patient.patient_id,
        service_provider=ORG_ID,
        service_type=HS_ID,
        identifier=[IDENTIFIER_1],
    )
    created_encounter = copy.deepcopy(ENCOUNTER_BASE)
    created_encounter.subject = patient.patient_id
    created_encounter.share_id = ShareId("share-for-relative")

    mock_patient_repo.get_patient_by_id.return_value = patient
    mock_organization_repo.get_organization_by_id.return_value = AsyncMock()
    mock_encounter_repo.create_encounter.return_value = created_encounter

    await encounter_service.create_encounter(put_request)

    relative_phone = patient.contact[0].telecom[0].value
    mock_notification_service.send_sms.assert_called_once_with(
        phone=relative_phone,
        content=f"Suivez le parcours aux urgences de votre proche: {BASE_URL}/sign-in/{created_encounter.share_id}",
    )


@pytest.mark.asyncio
async def test_create_encounter_patient_with_phone_and_contacts(
    encounter_service,
    mock_encounter_repo,
    mock_patient_repo,
    mock_organization_repo,
    mock_notification_service,
):
    """SMS sent to patient and all SMS relatives."""
    patient = PATIENT_3_WITH_CONTACTS
    put_request = PutEncounterRequest(
        subject=patient.patient_id,
        service_provider=ORG_ID,
        service_type=HS_ID,
        identifier=[IDENTIFIER_1],
    )
    created_encounter = copy.deepcopy(ENCOUNTER_BASE)
    created_encounter.subject = patient.patient_id
    created_encounter.share_id = ShareId("share-multi-relative")

    mock_patient_repo.get_patient_by_id.return_value = patient
    mock_organization_repo.get_organization_by_id.return_value = AsyncMock()
    mock_encounter_repo.create_encounter.return_value = created_encounter

    await encounter_service.create_encounter(put_request)

    expected_calls = [
        call(
            phone=patient.phone,
            content=f"Pour suivre l'avancée de votre parcours aux urgences du CHU de Limoges, cliquez sur ce lien: {BASE_URL}/sign-in/{created_encounter.encounter_id}",
        ),
        call(
            phone=patient.contact[0].telecom[0].value,
            content=f"Suivez le parcours aux urgences de votre proche: {BASE_URL}/sign-in/{created_encounter.share_id}",
        ),
        call(
            phone=patient.contact[1].telecom[0].value,
            content=f"Suivez le parcours aux urgences de votre proche: {BASE_URL}/sign-in/{created_encounter.share_id}",
        ),
    ]

    mock_notification_service.send_sms.assert_has_calls(expected_calls, any_order=True)
    assert mock_notification_service.send_sms.call_count == 3


@pytest.mark.asyncio
async def test_create_encounter_sms_error_does_not_stop(
    encounter_service,
    mock_encounter_repo,
    mock_patient_repo,
    mock_organization_repo,
    mock_notification_service,
):
    """Ensure failure sending one SMS doesn't stop others or the function."""
    patient = PATIENT_3_WITH_CONTACTS
    put_request = PutEncounterRequest(
        subject=patient.patient_id,
        service_provider=ORG_ID,
        service_type=HS_ID,
        identifier=[IDENTIFIER_1],
    )
    created_encounter = copy.deepcopy(ENCOUNTER_BASE)
    created_encounter.subject = patient.patient_id
    created_encounter.share_id = ShareId("share-error-test")

    mock_patient_repo.get_patient_by_id.return_value = patient
    mock_organization_repo.get_organization_by_id.return_value = AsyncMock()
    mock_encounter_repo.create_encounter.return_value = created_encounter

    mock_notification_service.send_sms.side_effect = [
        Exception("SMS provider down"),
        AsyncMock(),
        AsyncMock(),
    ]

    result_encounter = await encounter_service.create_encounter(put_request)
    assert result_encounter == created_encounter

    assert mock_notification_service.send_sms.call_count == 3


# --- check_and_send_delayed_sms Tests ---


@pytest.mark.asyncio
@freeze_time(FROZEN_NOW)
async def test_send_delayed_sms_when_discharged_over_2h_ago(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Check delayed SMS sent if discharged > 2h ago, patient has phone."""
    mock_encounter_repo.list_encounters.return_value = [
        copy.deepcopy(ENCOUNTER_DISCHARGED_3H_AGO)
    ]
    mock_patient_repo.get_patient_by_id.return_value = PATIENT_1_PHONE

    await encounter_service.check_and_send_delayed_sms()

    mock_notification_service.send_sms.assert_called_once_with(
        phone=PATIENT_1_PHONE.phone, content=ANY
    )

    assert (
        "forms.gle/utMiEhQnZnpFcrdj8"
        in mock_notification_service.send_sms.call_args[1]["content"]
    )

    mock_encounter_repo.save_encounter.assert_called_once()
    saved_encounter = mock_encounter_repo.save_encounter.call_args[0][0]
    assert saved_encounter.encounter_id == ENCOUNTER_DISCHARGED_3H_AGO.encounter_id
    assert saved_encounter.post_discharge_sms_sent_at == int(
        FROZEN_NOW.timestamp() * 1000
    )


@pytest.mark.asyncio
@freeze_time(FROZEN_NOW)
async def test_no_delayed_sms_when_patient_has_no_phone(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Check delayed SMS NOT sent but encounter marked if patient has no phone."""
    mock_encounter_repo.list_encounters.return_value = [
        copy.deepcopy(ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO)
    ]
    mock_patient_repo.get_patient_by_id.return_value = PATIENT_2_NO_PHONE

    await encounter_service.check_and_send_delayed_sms()

    mock_notification_service.send_sms.assert_not_called()

    mock_encounter_repo.save_encounter.assert_called_once()
    saved_encounter = mock_encounter_repo.save_encounter.call_args[0][0]
    assert (
        saved_encounter.encounter_id
        == ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO.encounter_id
    )
    assert saved_encounter.post_discharge_sms_sent_at == int(
        FROZEN_NOW.timestamp() * 1000
    )


@pytest.mark.asyncio
@freeze_time(FROZEN_NOW)
async def test_delayed_sms_no_sms_when_discharged_less_than_2h_ago(
    encounter_service, mock_encounter_repo, mock_notification_service
):
    """Check delayed SMS is NOT sent if discharged < 2h ago."""
    mock_encounter_repo.list_encounters.return_value = [
        copy.deepcopy(ENCOUNTER_DISCHARGED_1H_AGO)
    ]

    await encounter_service.check_and_send_delayed_sms()

    mock_notification_service.send_sms.assert_not_called()
    mock_encounter_repo.save_encounter.assert_not_called()


@pytest.mark.asyncio
@freeze_time(FROZEN_NOW)
async def test_delayed_sms_no_sms_when_already_sent(
    encounter_service, mock_encounter_repo, mock_notification_service
):
    """Check delayed SMS is NOT sent if it was already sent."""
    mock_encounter_repo.list_encounters.return_value = [
        copy.deepcopy(ENCOUNTER_SMS_ALREADY_SENT)
    ]

    await encounter_service.check_and_send_delayed_sms()

    mock_notification_service.send_sms.assert_not_called()
    mock_encounter_repo.save_encounter.assert_not_called()


@pytest.mark.asyncio
@freeze_time(FROZEN_NOW)
async def test_delayed_sms_no_sms_when_not_discharged(
    encounter_service, mock_encounter_repo, mock_notification_service
):
    """Check delayed SMS is NOT sent if the patient hasn't been discharged."""
    mock_encounter_repo.list_encounters.return_value = [
        copy.deepcopy(ENCOUNTER_NOT_DISCHARGED)
    ]

    await encounter_service.check_and_send_delayed_sms()

    mock_notification_service.send_sms.assert_not_called()
    mock_encounter_repo.save_encounter.assert_not_called()


@pytest.mark.asyncio
@freeze_time(FROZEN_NOW)
async def test_delayed_sms_handles_multiple_encounters_mixed_phones(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Check correct handling for multiple encounters with/without patient phones."""
    test_encounters = [
        copy.deepcopy(ENCOUNTER_DISCHARGED_3H_AGO),
        copy.deepcopy(ENCOUNTER_DISCHARGED_1H_AGO),
        copy.deepcopy(ENCOUNTER_SMS_ALREADY_SENT),
        copy.deepcopy(ENCOUNTER_NOT_DISCHARGED),
        copy.deepcopy(ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO),
    ]
    mock_encounter_repo.list_encounters.return_value = test_encounters

    patient_map = {
        PATIENT_ID_1: PATIENT_1_PHONE,
        PATIENT_ID_NO_PHONE: PATIENT_2_NO_PHONE,
    }

    async def get_patient_side_effect_multi(patient_id):
        return patient_map.get(patient_id)

    mock_patient_repo.get_patient_by_id.side_effect = get_patient_side_effect_multi

    await encounter_service.check_and_send_delayed_sms()

    mock_notification_service.send_sms.assert_called_once_with(
        phone=PATIENT_1_PHONE.phone, content=ANY
    )

    assert mock_encounter_repo.save_encounter.call_count == 2
    saved_call_args = mock_encounter_repo.save_encounter.call_args_list

    saved_encounter_1 = saved_call_args[0][0][0]
    saved_encounter_2 = saved_call_args[1][0][0]

    assert saved_encounter_1.encounter_id == ENCOUNTER_DISCHARGED_3H_AGO.encounter_id
    assert saved_encounter_1.post_discharge_sms_sent_at == int(
        FROZEN_NOW.timestamp() * 1000
    )

    assert (
        saved_encounter_2.encounter_id
        == ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO.encounter_id
    )
    assert saved_encounter_2.post_discharge_sms_sent_at == int(
        FROZEN_NOW.timestamp() * 1000
    )


@pytest.mark.asyncio
@freeze_time(FROZEN_NOW)
async def test_delayed_sms_error_fetching_patient_does_not_stop_processing(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Check that an error fetching one patient doesn't halt the process for others."""
    encounter_needs_sms_1 = copy.deepcopy(ENCOUNTER_DISCHARGED_3H_AGO)
    encounter_needs_sms_2 = copy.deepcopy(ENCOUNTER_DISCHARGED_3H_AGO)
    encounter_needs_sms_2.encounter_id = EncounterId("enc-needs-sms-2")
    encounter_needs_sms_2.share_id = ShareId("share-needs-sms-2")
    encounter_needs_sms_2.subject = PATIENT_ID_WITH_CONTACTS
    encounter_needs_sms_3 = copy.deepcopy(ENCOUNTER_NO_PHONE_DISCHARGED_3H_AGO)

    mock_encounter_repo.list_encounters.return_value = [
        encounter_needs_sms_1,
        encounter_needs_sms_2,
        encounter_needs_sms_3,
    ]

    patient_map_error = {
        PATIENT_ID_1: PATIENT_1_PHONE,
        PATIENT_ID_WITH_CONTACTS: None,
        PATIENT_ID_NO_PHONE: PATIENT_2_NO_PHONE,
    }

    async def get_patient_side_effect_error(patient_id):
        if patient_id == PATIENT_ID_WITH_CONTACTS:
            raise HTTPException(status_code=404, detail="Patient not found")
        return patient_map_error.get(patient_id)

    mock_patient_repo.get_patient_by_id.side_effect = get_patient_side_effect_error

    await encounter_service.check_and_send_delayed_sms()

    mock_notification_service.send_sms.assert_called_once_with(
        phone=PATIENT_1_PHONE.phone, content=ANY
    )

    assert mock_encounter_repo.save_encounter.call_count == 2
    saved_call_args = mock_encounter_repo.save_encounter.call_args_list
    saved_encounter_ids = {call[0][0].encounter_id for call in saved_call_args}
    assert encounter_needs_sms_1.encounter_id in saved_encounter_ids
    assert encounter_needs_sms_3.encounter_id in saved_encounter_ids


@pytest.mark.asyncio
async def test_update_encounter_add_first_instructions_sends_sms(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Test adding initial discharge instructions triggers SMS to patient with phone."""
    initial_encounter = copy.deepcopy(ENCOUNTER_FOR_UPDATE)
    patient = PATIENT_1_PHONE
    patch = PatchEncounterRequest(discharge=Discharge(instructions=[ATTACHMENT_1]))

    mock_encounter_repo.get_encounter_by_id.return_value = initial_encounter
    mock_patient_repo.get_patient_by_id.return_value = patient

    updated_encounter = await encounter_service.update_encounter(
        initial_encounter.encounter_id, patch
    )

    # Check encounter saved with instructions
    mock_encounter_repo.save_encounter.assert_called_once()
    saved_encounter = mock_encounter_repo.save_encounter.call_args[0][0]
    assert saved_encounter.discharge is not None
    assert saved_encounter.discharge.instructions == [ATTACHMENT_1]

    # Check SMS sent to patient
    mock_notification_service.send_sms.assert_called_once()
    call_args = mock_notification_service.send_sms.call_args
    assert call_args[1]["phone"] == patient.phone
    assert ATTACHMENT_1.name in call_args[1]["content"]
    assert f"/consignes/{ATTACHMENT_1.id}.pdf" in call_args[1]["content"]


@pytest.mark.asyncio
async def test_update_encounter_append_instructions_sends_sms(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Test adding new instructions to existing ones appends and triggers SMS with all instructions."""
    initial_encounter = copy.deepcopy(ENCOUNTER_FOR_UPDATE)
    initial_encounter.discharge = Discharge(
        instructions=[ATTACHMENT_1]
    )  # Start with one instruction
    patient = PATIENT_1_PHONE
    patch = PatchEncounterRequest(
        discharge=Discharge(instructions=[ATTACHMENT_2, ATTACHMENT_3])
    )

    mock_encounter_repo.get_encounter_by_id.return_value = initial_encounter
    mock_patient_repo.get_patient_by_id.return_value = patient

    await encounter_service.update_encounter(initial_encounter.encounter_id, patch)

    # Check encounter saved with appended instructions
    mock_encounter_repo.save_encounter.assert_called_once()
    saved_encounter = mock_encounter_repo.save_encounter.call_args[0][0]
    assert saved_encounter.discharge is not None
    assert saved_encounter.discharge.instructions == [
        ATTACHMENT_1,
        ATTACHMENT_2,
        ATTACHMENT_3,
    ]

    # Check SMS sent with all instructions
    mock_notification_service.send_sms.assert_called_once()
    call_args = mock_notification_service.send_sms.call_args
    assert call_args[1]["phone"] == patient.phone
    assert ATTACHMENT_1.name in call_args[1]["content"]
    assert ATTACHMENT_2.name in call_args[1]["content"]
    assert ATTACHMENT_3.name in call_args[1]["content"]
    assert f"/consignes/{ATTACHMENT_1.id}.pdf" in call_args[1]["content"]
    assert f"/consignes/{ATTACHMENT_2.id}.pdf" in call_args[1]["content"]
    assert f"/consignes/{ATTACHMENT_3.id}.pdf" in call_args[1]["content"]


@pytest.mark.asyncio
async def test_update_encounter_add_instructions_no_patient_phone(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Test adding instructions doesn't trigger SMS if patient has no phone."""
    initial_encounter = copy.deepcopy(ENCOUNTER_FOR_UPDATE)
    initial_encounter.subject = (
        PATIENT_2_NO_PHONE.patient_id
    )  # Assign patient without phone
    patient = PATIENT_2_NO_PHONE
    patch = PatchEncounterRequest(discharge=Discharge(instructions=[ATTACHMENT_1]))

    mock_encounter_repo.get_encounter_by_id.return_value = initial_encounter
    mock_patient_repo.get_patient_by_id.return_value = patient

    await encounter_service.update_encounter(initial_encounter.encounter_id, patch)

    # Check encounter saved with instructions
    mock_encounter_repo.save_encounter.assert_called_once()
    saved_encounter = mock_encounter_repo.save_encounter.call_args[0][0]
    assert saved_encounter.discharge is not None
    assert saved_encounter.discharge.instructions == [ATTACHMENT_1]

    # Check SMS was NOT sent
    mock_notification_service.send_sms.assert_not_called()


@pytest.mark.asyncio
async def test_update_encounter_add_duplicate_instructions(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Test adding duplicate instructions doesn't change the list or send SMS."""
    initial_encounter = copy.deepcopy(ENCOUNTER_FOR_UPDATE)
    initial_encounter.discharge = Discharge(
        instructions=[ATTACHMENT_1, ATTACHMENT_2]
    )  # Start with two instructions
    patient = PATIENT_1_PHONE
    # Patch contains one existing and one new instruction
    patch = PatchEncounterRequest(
        discharge=Discharge(instructions=[ATTACHMENT_2, ATTACHMENT_3])
    )

    mock_encounter_repo.get_encounter_by_id.return_value = initial_encounter
    mock_patient_repo.get_patient_by_id.return_value = patient

    await encounter_service.update_encounter(initial_encounter.encounter_id, patch)

    # Check encounter saved with only the new instruction appended
    mock_encounter_repo.save_encounter.assert_called_once()
    saved_encounter = mock_encounter_repo.save_encounter.call_args[0][0]
    assert saved_encounter.discharge is not None
    assert saved_encounter.discharge.instructions == [
        ATTACHMENT_1,
        ATTACHMENT_2,
        ATTACHMENT_3,
    ]

    # Check SMS was sent (because a new instruction *was* added)
    mock_notification_service.send_sms.assert_called_once()
    call_args = mock_notification_service.send_sms.call_args
    assert call_args[1]["phone"] == patient.phone
    # Check content includes all three instructions
    assert ATTACHMENT_1.name in call_args[1]["content"]
    assert ATTACHMENT_2.name in call_args[1]["content"]
    assert ATTACHMENT_3.name in call_args[1]["content"]


@pytest.mark.asyncio
async def test_update_encounter_other_fields_no_sms(
    encounter_service, mock_encounter_repo, mock_notification_service
):
    """Test updating non-discharge fields doesn't trigger discharge SMS."""
    initial_encounter = copy.deepcopy(ENCOUNTER_FOR_UPDATE)
    initial_encounter.discharge = Discharge(
        instructions=[ATTACHMENT_1]
    )  # Has instructions
    patch = PatchEncounterRequest(planned_length=120)  # Update unrelated field

    mock_encounter_repo.get_encounter_by_id.return_value = initial_encounter
    # No need to mock patient repo as get_patient_by_id shouldn't be called

    await encounter_service.update_encounter(initial_encounter.encounter_id, patch)

    # Check encounter saved with updated field
    mock_encounter_repo.save_encounter.assert_called_once()
    saved_encounter = mock_encounter_repo.save_encounter.call_args[0][0]
    assert saved_encounter.planned_length == 120
    assert saved_encounter.discharge.instructions == [
        ATTACHMENT_1
    ]  # Instructions unchanged

    # Check SMS was NOT sent
    mock_notification_service.send_sms.assert_not_called()


@pytest.mark.asyncio
async def test_update_encounter_empty_instructions_list_no_sms(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Test sending patch with empty instructions list doesn't send SMS or clear existing."""
    initial_encounter = copy.deepcopy(ENCOUNTER_FOR_UPDATE)
    initial_encounter.discharge = Discharge(
        instructions=[ATTACHMENT_1]
    )  # Start with one instruction
    patient = PATIENT_1_PHONE
    patch = PatchEncounterRequest(
        discharge=Discharge(instructions=[])
    )  # Empty list in patch

    mock_encounter_repo.get_encounter_by_id.return_value = initial_encounter
    # Need to mock patient repo because the code *might* fetch it, even if no SMS is sent
    mock_patient_repo.get_patient_by_id.return_value = patient

    await encounter_service.update_encounter(initial_encounter.encounter_id, patch)

    # Check encounter saved, instructions unchanged
    mock_encounter_repo.save_encounter.assert_called_once()
    saved_encounter = mock_encounter_repo.save_encounter.call_args[0][0]
    assert saved_encounter.discharge is not None
    assert saved_encounter.discharge.instructions == [ATTACHMENT_1]

    # Check SMS was NOT sent
    mock_notification_service.send_sms.assert_not_called()


# --- Quiet Hours Tests ---


@pytest.mark.asyncio
@freeze_time("2024-03-10 02:00:00+01:00")  # 2 AM Paris time (CET) - within quiet hours
async def test_delayed_sms_not_sent_during_quiet_hours_paris(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Test that post-discharge SMS is NOT sent during quiet hours (23h-8h Paris)."""
    # Need fresh copies to avoid side effects if other tests modified shared fixtures
    encounter_ready = copy.deepcopy(ENCOUNTER_DISCHARGED_3H_AGO)
    patient = PATIENT_1_PHONE  # Assuming this patient has a phone number

    mock_encounter_repo.list_encounters.return_value = [encounter_ready]
    mock_patient_repo.get_patient_by_id.return_value = (
        patient  # Mock needed even if SMS not sent
    )

    # Reset mocks before test
    mock_notification_service.reset_mock()
    mock_encounter_repo.reset_mock()

    await encounter_service.check_and_send_delayed_sms()

    # Assert SMS was NOT sent to patient or relatives
    mock_notification_service.send_sms.assert_not_called()
    # Assert encounter was NOT saved (SMS sent timestamp not updated)
    mock_encounter_repo.save_encounter.assert_not_called()


@pytest.mark.asyncio
@freeze_time(
    "2024-03-10 10:00:00+01:00"
)  # 10 AM Paris time (CET) - outside quiet hours
async def test_delayed_sms_sent_outside_quiet_hours_paris_with_relatives(
    encounter_service, mock_encounter_repo, mock_patient_repo, mock_notification_service
):
    """Test that post-discharge SMS is sent outside quiet hours (23h-8h Paris) to patient and relatives."""
    # Use a patient with contacts for this test
    # Need fresh copies to avoid side effects if other tests modified shared fixtures
    encounter_ready = copy.deepcopy(ENCOUNTER_DISCHARGED_3H_AGO)
    patient_with_contacts = copy.deepcopy(PATIENT_3_WITH_CONTACTS)
    encounter_ready.subject = (
        patient_with_contacts.patient_id
    )  # Assign patient with contacts

    mock_encounter_repo.list_encounters.return_value = [encounter_ready]
    mock_patient_repo.get_patient_by_id.return_value = patient_with_contacts

    # Reset mocks for call count verification
    mock_notification_service.reset_mock()
    mock_encounter_repo.reset_mock()

    await encounter_service.check_and_send_delayed_sms()

    # Expected calls: one to patient, two to relatives
    expected_calls = [
        call(phone=patient_with_contacts.phone, content=POST_DISCHARGE_PATIENT_SMS),
        call(
            phone=patient_with_contacts.contact[0].telecom[0].value,
            content=POST_DISCHARGE_RELATIVE_SMS,
        ),
        call(
            phone=patient_with_contacts.contact[1].telecom[0].value,
            content=POST_DISCHARGE_RELATIVE_SMS,
        ),
    ]

    # Assert SMS was sent to patient and relatives
    mock_notification_service.send_sms.assert_has_calls(expected_calls, any_order=True)
    assert mock_notification_service.send_sms.call_count == 3

    # Assert encounter was saved with the sent timestamp
    mock_encounter_repo.save_encounter.assert_called_once()
    saved_encounter = mock_encounter_repo.save_encounter.call_args[0][0]
    assert isinstance(saved_encounter, Encounter)
    assert saved_encounter.encounter_id == encounter_ready.encounter_id
    assert saved_encounter.post_discharge_sms_sent_at is not None
    # Check timestamp is close to 'now' (frozen time)
    # Use the frozen time directly for comparison
    frozen_time_dt = datetime(
        2024, 3, 10, 10, 0, 0, tzinfo=timezone(timedelta(hours=1))
    )
    # Convert frozen time to UTC timestamp in milliseconds for comparison
    frozen_ts_utc_ms = int(frozen_time_dt.astimezone(timezone.utc).timestamp() * 1000)
    assert (
        abs(saved_encounter.post_discharge_sms_sent_at - frozen_ts_utc_ms) < 1000
    )  # Allow 1s diff
